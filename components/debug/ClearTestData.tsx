'use client';

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON>Content, CardHeader, CardTitle } from "@/components/ui/card";
import { Trash2, RefreshCw } from "lucide-react";
import { useToast } from "@/components/ui/use-toast";
import { useRouter } from "next/navigation";
import { useEditOrder } from "@/components/providers/EditOrderContext";

export default function ClearTestData() {
  const { toast } = useToast();
  const router = useRouter();
  const { clearEditOrder } = useEditOrder();

  const clearAllTestData = () => {
    try {
      // Clear edit order context
      clearEditOrder();
      
      // Clear edit sessions
      localStorage.removeItem('current_edit_session');
      
      // Clear dev test mode
      localStorage.removeItem('devTestMode');
      
      // Clear any UI state data
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('ui_state_') || 
            key.includes('order') || 
            key.includes('test') || 
            key.includes('edit')) {
          localStorage.removeItem(key);
        }
      });
      
      // Clear any pending order data
      if ((window as any).pendingOrderData) {
        delete (window as any).pendingOrderData;
      }
      
      toast({
        title: "Test Data Cleared",
        description: "All test data has been removed. Refreshing page...",
      });
      
      // Refresh the page after a short delay
      setTimeout(() => {
        window.location.reload();
      }, 1000);
      
    } catch (error) {
      console.error('Error clearing test data:', error);
      toast({
        title: "Error",
        description: "Failed to clear test data. Check console for details.",
        variant: "destructive",
      });
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Trash2 className="h-5 w-5" />
          Clear Test Data
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <p className="text-sm text-muted-foreground">
          Remove all test data including cached orders, edit sessions, and UI state from localStorage.
        </p>
        
        <div className="flex gap-2">
          <Button 
            onClick={clearAllTestData}
            variant="destructive"
            className="flex items-center gap-2"
          >
            <Trash2 className="h-4 w-4" />
            Clear All Test Data
          </Button>
          
          <Button 
            onClick={() => router.push('/ordering')}
            variant="outline"
            className="flex items-center gap-2"
          >
            <RefreshCw className="h-4 w-4" />
            Go to Ordering
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}