import { getPouchDB } from '@/lib/db/pouchdb-instance';
import { PrinterConfig } from '@/lib/services/kitchen-print-service';

const PRINTER_SETTINGS_DOC_ID = 'printer_settings';

interface PrinterSettingsDoc {
  _id: string;
  _rev?: string;
  printers: PrinterConfig[];
}

export const savePrinterSettings = async (printers: PrinterConfig[]): Promise<void> => {
  try {
    // Ensure database is ready before attempting to save
    const { getMainDbInstance } = await import('../../pouchdb-instance');
    const dbInstance = getMainDbInstance();

    // Wait for database initialization if needed
    if (!dbInstance.isInitialized) {
      console.log('🔄 [savePrinterSettings] Database not initialized, waiting...');
      const currentRestaurantId = dbInstance.getCurrentRestaurantId();
      if (currentRestaurantId) {
        await dbInstance.waitForInitialization(currentRestaurantId, 10000);
      } else {
        throw new Error('No restaurant ID available for saving printer settings');
      }
    }

    const db = getPouchDB();

    // Retry logic to handle document conflicts
    let retries = 3;
    while (retries > 0) {
      try {
        const doc = await db.get<PrinterSettingsDoc>(PRINTER_SETTINGS_DOC_ID).catch(err => {
          if (err.name === 'not_found' || err.status === 404) {
            return {
              _id: PRINTER_SETTINGS_DOC_ID,
              printers: [],
            };
          }
          throw err;
        });

        await db.put({
          ...doc,
          printers,
        });

        console.log('✅ [savePrinterSettings] Printer settings saved successfully');
        return; // Success, exit retry loop

      } catch (error: any) {
        if (error.name === 'conflict' && retries > 1) {
          console.warn(`⚠️ [savePrinterSettings] Document conflict, retrying... (${retries - 1} attempts left)`);
          retries--;
          // Wait a bit before retrying
          await new Promise(resolve => setTimeout(resolve, 100));
          continue;
        }

        console.error('❌ [savePrinterSettings] Failed to save printer settings:', error);
        throw error;
      }
    }
  } catch (error) {
    console.error('❌ [savePrinterSettings] Critical error saving printer settings:', error);
    throw error;
  }
};

export const loadPrinterSettings = async (): Promise<PrinterConfig[]> => {
  try {
    // Ensure database is ready before attempting to load
    const { getMainDbInstance } = await import('../../pouchdb-instance');
    const dbInstance = getMainDbInstance();

    // Wait for database initialization if needed
    if (!dbInstance.isInitialized) {
      console.log('🔄 [loadPrinterSettings] Database not initialized, waiting...');
      const currentRestaurantId = dbInstance.getCurrentRestaurantId();
      if (currentRestaurantId) {
        await dbInstance.waitForInitialization(currentRestaurantId, 10000);
      } else {
        console.warn('⚠️ [loadPrinterSettings] No restaurant ID available, returning empty printers');
        return [];
      }
    }

    const db = getPouchDB();
    const doc = await db.get<PrinterSettingsDoc>(PRINTER_SETTINGS_DOC_ID);
    return doc.printers || [];
  } catch (err: any) {
    if (err.name === 'not_found' || err.status === 404) {
      console.log('📄 [loadPrinterSettings] No printer settings document found, returning empty array');
      return []; // No settings saved yet
    }
    console.error('❌ [loadPrinterSettings] Failed to load printer settings:', err);
    // Return empty array instead of throwing to prevent app crashes
    return [];
  }
};
